#!/bin/bash
set -e

aws ecr get-login-password --region us-east-1 --profile hospice-os-prod | docker login --username AWS --password-stdin 239026187112.dkr.ecr.us-east-1.amazonaws.com

docker build --platform linux/amd64 -f DOCKERFILE.worker -t hospice-os-temporal-worker .
docker tag hospice-os-temporal-worker:latest 239026187112.dkr.ecr.us-east-1.amazonaws.com/hospice-os-temporal-worker:latest 
docker push 239026187112.dkr.ecr.us-east-1.amazonaws.com/hospice-os-temporal-worker:latest

# update the service to use the new image
aws ecs update-service --cluster hospice-os-temporal-worker-prod-TemporalWorkerCluster --service hospice-os-temporal-worker-prod-TemporalWorkerService --force-new-deployment --profile hospice-os-prod
