#!/bin/bash
set -e

aws ecr get-login-password --region us-east-1 --profile hospice-os-staging | docker login --username AWS --password-stdin 690071479855.dkr.ecr.us-east-1.amazonaws.com

docker build --platform linux/amd64 -f 'DOCKERFILE.worker' -t hospice-os-temporal-worker .
docker tag hospice-os-temporal-worker:latest 690071479855.dkr.ecr.us-east-1.amazonaws.com/hospice-os-temporal-worker:latest 
docker push 690071479855.dkr.ecr.us-east-1.amazonaws.com/hospice-os-temporal-worker:latest

# update the service to use the new image
aws ecs update-service --cluster hospice-os-temporal-worker-staging-TemporalWorkerCluster --service hospice-os-temporal-worker-staging-TemporalWorkerService --force-new-deployment --profile hospice-os-staging
