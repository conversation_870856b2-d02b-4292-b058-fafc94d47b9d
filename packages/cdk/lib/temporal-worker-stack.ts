import * as cdk from "aws-cdk-lib";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as iam from "aws-cdk-lib/aws-iam";
import * as logs from "aws-cdk-lib/aws-logs";
import * as ssm from "aws-cdk-lib/aws-ssm";
import * as cloudwatch from "aws-cdk-lib/aws-cloudwatch";
import { Construct } from "constructs";

interface TemporalWorkerStackProps extends cdk.StackProps {
  vpc: ec2.IVpc;
  stage: string;
}

export class TemporalWorkerStack extends cdk.Stack {
  public readonly service: ecs.FargateService;

  constructor(scope: Construct, id: string, props: TemporalWorkerStackProps) {
    super(scope, id, props);

    const { vpc, stage } = props;

    // Create dedicated ECS cluster for Temporal workers
    const cluster = new ecs.Cluster(this, "TemporalWorkerCluster", {
      vpc,
      clusterName: `${this.stackName}-temporal-worker-cluster`,
      containerInsights: true,
      enableFargateCapacityProviders: true,
    });

    // Create log group for Temporal workers
    const logGroup = new logs.LogGroup(this, "TemporalWorkerLogGroup", {
      logGroupName: `/ecs/temporal-worker/${this.stackName}`,
      retention: logs.RetentionDays.ONE_WEEK,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    // Create task definition for Temporal worker
    const taskDefinition = new ecs.FargateTaskDefinition(
      this,
      "TemporalWorkerTaskDef",
      {
        memoryLimitMiB: 2048,
        cpu: 1024,
        family: `${this.stackName}-temporal-worker`,
      },
    );

    // Add container to task definition
    const container = taskDefinition.addContainer("temporal-worker", {
      image: ecs.ContainerImage.fromAsset("../../", {
        file: "DOCKERFILE.worker",
        platform: ecs.Platform.LINUX_AMD64,
        exclude: [
          "packages/cdk/cdk.out",
          "packages/cdk/node_modules",
          "**/node_modules",
          "**/.git",
        ],
      }),
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: "temporal-worker",
        logGroup,
      }),
      environment: {
        NODE_ENV: process.env.NODE_ENV || "production",
        STAGE: stage,
      },
      healthCheck: {
        command: ["CMD-SHELL", "node -e 'process.exit(0)'"],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        retries: 3,
        startPeriod: cdk.Duration.seconds(60),
      },
    });

    // Grant permissions to read from Parameter Store
    taskDefinition.taskRole?.addToPrincipalPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParametersByPath",
        ],
        resources: [
          `arn:aws:ssm:${this.region}:${this.account}:parameter/${this.stackName}/*`,
          `arn:aws:ssm:${this.region}:${this.account}:parameter/shared/*`,
        ],
      }),
    );

    // Grant permissions for S3 access (for PDF processing)
    taskDefinition.taskRole?.addToPrincipalPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"],
        resources: ["arn:aws:s3:::*/*"],
      }),
    );

    // Grant permissions for Textract (for PDF processing)
    taskDefinition.taskRole?.addToPrincipalPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          "textract:StartDocumentTextDetection",
          "textract:GetDocumentTextDetection",
          "textract:StartDocumentAnalysis",
          "textract:GetDocumentAnalysis",
        ],
        resources: ["*"],
      }),
    );

    // Create security group for Temporal worker
    const securityGroup = new ec2.SecurityGroup(
      this,
      "TemporalWorkerSecurityGroup",
      {
        vpc,
        description: "Security group for Temporal worker",
        allowAllOutbound: true,
      },
    );

    // Create Fargate service with auto-scaling
    this.service = new ecs.FargateService(this, "TemporalWorkerService", {
      cluster,
      taskDefinition,
      desiredCount: 2, // Start with minimum 2 instances
      serviceName: `${this.stackName}-temporal-worker`,
      securityGroups: [securityGroup],
      vpcSubnets: {
        subnets: vpc.privateSubnets,
      },
      enableExecuteCommand: true,
      capacityProviderStrategies: [
        {
          capacityProvider: "FARGATE_SPOT",
          weight: 2,
          base: 0,
        },
        {
          capacityProvider: "FARGATE",
          weight: 1,
          base: 2, // Always have at least 2 on-demand instances
        },
      ],
    });

    // Configure auto-scaling
    const scaling = this.service.autoScaleTaskCount({
      minCapacity: 2,
      maxCapacity: 6,
    });

    // Scale based on CPU utilization
    scaling.scaleOnCpuUtilization("CpuScaling", {
      targetUtilizationPercent: 70,
      scaleInCooldown: cdk.Duration.seconds(300),
      scaleOutCooldown: cdk.Duration.seconds(60),
    });

    // Scale based on memory utilization
    scaling.scaleOnMemoryUtilization("MemoryScaling", {
      targetUtilizationPercent: 70,
      scaleInCooldown: cdk.Duration.seconds(300),
      scaleOutCooldown: cdk.Duration.seconds(60),
    });

    // Add CloudWatch dashboard for monitoring
    const dashboard = new cloudwatch.Dashboard(
      this,
      "TemporalWorkerDashboard",
      {
        dashboardName: `${this.stackName}-temporal-worker-dashboard`,
      },
    );

    dashboard.addWidgets(
      new cloudwatch.GraphWidget({
        title: "Temporal Worker CPU Utilization",
        left: [
          this.service.metricCpuUtilization({
            statistic: "Average",
            period: cdk.Duration.minutes(5),
          }),
        ],
      }),
      new cloudwatch.GraphWidget({
        title: "Temporal Worker Memory Utilization",
        left: [
          this.service.metricMemoryUtilization({
            statistic: "Average",
            period: cdk.Duration.minutes(5),
          }),
        ],
      }),
      new cloudwatch.GraphWidget({
        title: "Temporal Worker Task Count",
        left: [
          new cloudwatch.Metric({
            namespace: "AWS/ECS",
            metricName: "ServiceRunningTaskCount",
            dimensionsMap: {
              ServiceName: this.service.serviceName,
              ClusterName: cluster.clusterName,
            },
            statistic: "Average",
            period: cdk.Duration.minutes(5),
          }),
        ],
      }),
    );

    // Output service ARN
    new cdk.CfnOutput(this, "TemporalWorkerServiceArn", {
      value: this.service.serviceArn,
      description: "ARN of the Temporal Worker ECS Service",
    });

    // Output log group
    new cdk.CfnOutput(this, "TemporalWorkerLogGroupOutput", {
      value: logGroup.logGroupName,
      description: "CloudWatch Log Group for Temporal Worker",
    });
  }
}
